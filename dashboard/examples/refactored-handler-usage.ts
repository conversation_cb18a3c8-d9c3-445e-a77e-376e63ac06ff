/**
 * Examples of using the refactored define<PERSON>andler with destructuring syntax
 * This demonstrates the clean, single-line handler definition with full type safety
 */

import defineHandler from "@/lib/requestHandler";
import { z } from "zod";

// Example 1: Simple query parameter validation with destructuring
export const GET = defineHandler(
  async ({ getQuery, json }) => {
    // Type-safe access to validated query parameters
    const id = getQuery("id"); // string (fully typed!)
    const name = getQuery("name"); // string | undefined (fully typed!)

    return json({
      message: `Hello ${name || "Anonymous"}`,
      id,
    });
  },
  (z) => ({
    query: z.object({
      id: z.string(),
      name: z.string().optional(),
    }),
  })
);

// Example 2: Body validation with destructuring
export const POST = defineHandler(
  async ({ getJSON, sendSuccess }) => {
    // Type-safe access to validated body
    const body = await getJSON(); // { title: string, content: string, published: boolean }

    // Simulate saving to database
    const post = {
      id: Math.random().toString(36),
      ...body,
      createdAt: new Date().toISOString(),
    };

    return sendSuccess(post);
  },
  (z) => ({
    body: z.object({
      title: z.string(),
      content: z.string(),
      published: z.boolean().default(false),
    }),
  })
);

// Example 3: Combined query and body validation
export const PUT = defineHandler(
  async ({ getQuery, getJSON, json, sendError }) => {
    const id = getQuery("id");
    const updates = await getJSON();

    if (!updates.title && !updates.content) {
      return sendError("At least one field must be provided for update");
    }

    return json({
      message: "Post updated successfully",
      id,
      updates,
    });
  },
  (z) => ({
    query: z.object({
      id: z.string(),
    }),
    body: z.object({
      title: z.string().optional(),
      content: z.string().optional(),
    }),
  })
);

// Example 4: File upload with validation
export const UPLOAD = defineHandler(
  async ({ getFile, json, sendError }) => {
    const avatar = await getFile("avatar"); // File | null
    const document = await getFile("document"); // File

    if (!avatar) {
      return sendError("Avatar is required");
    }

    return json({
      message: "Files uploaded successfully",
      avatarSize: avatar.size,
      documentName: document.name,
    });
  },
  (z) => ({
    files: {
      avatar: "ext:jpg,png,gif|max:5mb",
      document: z.instanceof(File),
    },
  })
);

// Example 5: Access to all context properties with destructuring
export const FULL_CONTEXT = defineHandler(
  async ({ getQuery, json, getHeader, cookies, sendUnauthorize, db }) => {
    const token = getQuery("token");
    const userAgent = getHeader("user-agent");
    const sessionId = cookies.get("session");

    if (!sessionId) {
      return sendUnauthorize("Session required");
    }

    // Access to database and environment
    return json({
      token,
      userAgent,
      sessionId,
      hasDatabase: !!db,
    });
  },
  (z) => ({
    query: z.object({
      token: z.string(),
    }),
  })
);

// Example 6: Handler without validation (untyped)
export const SIMPLE = defineHandler(async ({ json, getMethod }) => {
  return json({
    message: "This handler has no validation",
    method: getMethod(),
  });
});

// Example 7: Parameterless handler
export const HEALTH = defineHandler(() => {
  return Response.json({ status: "ok", timestamp: new Date().toISOString() });
});
