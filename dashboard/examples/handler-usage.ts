/**
 * Examples of using the refactored define<PERSON>andler with destructuring syntax
 */

import { createHand<PERSON> } from "@/lib/requestHandler";
import { z } from "zod";

// Example 1: Simple query parameter validation with destructuring
const simpleHandler = createHandler({
  query: z.object({
    id: z.string(),
    name: z.string().optional(),
  })
});

export const GET = simpleHandler(async ({ getQuery, json }) => {
  // Type-safe access to validated query parameters
  const id = getQuery("id");        // string
  const name = getQuery("name");    // string | undefined
  
  return json({
    message: `Hello ${name || 'Anonymous'}`,
    id
  });
});

// Example 2: Body validation with destructuring
const postHandler = createHandler({
  body: z.object({
    title: z.string(),
    content: z.string(),
    published: z.boolean().default(false),
  })
});

export const POST = postHandler(async ({ getJSO<PERSON>, json, sendSuccess }) => {
  // Type-safe access to validated body
  const body = await getJSON();  // { title: string, content: string, published: boolean }
  
  // Simulate saving to database
  const post = {
    id: Math.random().toString(36),
    ...body,
    createdAt: new Date().toISOString()
  };
  
  return sendSuccess(post);
});

// Example 3: Combined query and body validation
const updateHandler = createHandler({
  query: z.object({
    id: z.string(),
  }),
  body: z.object({
    title: z.string().optional(),
    content: z.string().optional(),
  })
});

export const PUT = updateHandler(async ({ getQuery, getJSON, json, sendError }) => {
  const id = getQuery("id");
  const updates = await getJSON();
  
  if (!updates.title && !updates.content) {
    return sendError("At least one field must be provided for update");
  }
  
  return json({
    message: "Post updated successfully",
    id,
    updates
  });
});

// Example 4: File upload with validation
const uploadHandler = createHandler({
  files: {
    avatar: "ext:jpg,png,gif|max:5mb",
    document: z.instanceof(File)
  }
});

export const UPLOAD = uploadHandler(async ({ getFile, json, sendError }) => {
  const avatar = await getFile("avatar");    // File | null
  const document = await getFile("document"); // File
  
  if (!avatar) {
    return sendError("Avatar is required");
  }
  
  return json({
    message: "Files uploaded successfully",
    avatarSize: avatar.size,
    documentName: document.name
  });
});

// Example 5: Access to all context properties with destructuring
const fullContextHandler = createHandler({
  query: z.object({
    token: z.string(),
  })
});

export const FULL_CONTEXT = fullContextHandler(async ({ 
  getQuery, 
  json, 
  getHeader, 
  cookies, 
  redirect, 
  sendUnauthorize,
  db,
  env 
}) => {
  const token = getQuery("token");
  const userAgent = getHeader("user-agent");
  const sessionId = cookies.get("session");
  
  if (!sessionId) {
    return sendUnauthorize("Session required");
  }
  
  // Access to database and environment
  const dbInstance = db();
  const apiKey = env.API_KEY;
  
  return json({
    token,
    userAgent,
    sessionId,
    hasApiKey: !!apiKey
  });
});
