import { integer, text, pgTable } from "drizzle-orm/pg-core";
import commonFields from "./common";
import { relations } from "drizzle-orm";

export const users = pgTable("users", {
    ...commonFields,
    email: text("email").notNull().unique(),
    password: text("password"),
    firstName: text("first_name").notNull(),
    lastName: text("last_name").notNull(),
    status: text("status").notNull()
});

export const agencies = pgTable("agencies", {
    ...commonFields,
    userId: text("user_id").references(() => users.id).notNull(),
    refreshToken: text("refresh_token").notNull(),
    accessToken: text("access_token").notNull(),
    expiresAt: integer("expires_at").notNull(),
    companyId: text("company_id").notNull(),
});

export const subAccounts = pgTable("sub_accounts", {
    ...commonFields,
    agencyId: text("agency_id").references(() => agencies.id).notNull(),
    accountId: text("account_id").notNull(),
    accessToken: text("access_token"),
    expiresAt: integer("expires_at"),
});


export const userRelations = relations(users, ({ many }) => ({
    agencies: many(agencies),
    subAccounts: many(subAccounts),
}));

export const agencyRelations = relations(agencies, ({ many }) => ({
    subAccounts: many(subAccounts),
}));

export const subAccountRelations = relations(subAccounts, ({ one }) => ({
    agency: one(agencies, {
        fields: [subAccounts.agencyId],
        references: [agencies.id],
    }),
}));