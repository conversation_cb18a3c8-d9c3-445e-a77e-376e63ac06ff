import { CloudflareContext, getCloudflareContext } from "@opennextjs/cloudflare";
import { NextRequest, NextResponse } from "next/server";
import { z, ZodTypeAny } from "zod";
import type { z as ZodType } from "zod";
import { useDB } from "./db/drizzle";
import * as DBSchema from "./db/schema";

// --- Type Inference Utilities ---
type HandlerValidationSchemas = {
  query?: ZodTypeAny;
  body?: ZodTypeAny;
  files?: Record<string, string | ZodTypeAny>;
};

type HandlerContextBase = {
  request: NextRequest;
  env: CloudflareContext['env'];
  response: NextResponse;
  cf: CloudflareContext['cf'];
  url: URL;
  cookies: {
    get: (name: string) => string | undefined;
    set: (name: string, value: string, options?: Record<string, unknown>) => void;
  };
  getHeader: (name: string) => string | null;
  getHeaders: () => Record<string, string>;
  setHeader: (name: string, value: string) => void;
  getMethod: () => string;
  getPath: () => string;
  redirect: (url: string, status?: number) => Response;
  json: (data: unknown, status?: number) => Response;
  text: (data: string, status?: number) => Response;
  sendError: (message: string, status?: number) => Response;
  sendSuccess: (data: unknown, status?: number) => Response;
  sendUnauthorize: (message?: string) => Response;
  db: typeof useDB;
  dbSchema: typeof DBSchema;
};

type HandlerContextTyped<Q, B, F> = HandlerContextBase & {
  getQuery: <K extends keyof Q & string>(name: K) => Q[K];
  getQueries: () => Q;
  getJSON: () => Promise<B>;
  getFormData: () => Promise<FormData>;
  getFile: <K extends keyof F & string>(name: K) => Promise<F[K]>;
  getFiles: () => Promise<F>;
  validated: {
    query: Q;
    body: B;
    files: F;
  };
};

type HandlerContextUntyped = HandlerContextBase & {
  getQuery: (name: string) => string | null;
  getQueries: () => Record<string, string | string[]>;
  getJSON: () => Promise<unknown>;
  getFormData: () => Promise<FormData>;
  getFile: (name: string) => Promise<File | null>;
  getFiles: () => Promise<File[]>;
  validated: {
    query?: unknown;
    body?: unknown;
    files?: unknown;
  };
};

/**
 * Handler context passed to the callback, with helper methods for query, body, files, cookies, headers, and common responses.
 */
export interface HandlerContext {
  request: NextRequest;
  env: CloudflareContext['env'];
  response: NextResponse;
  cf: CloudflareContext['cf'];
  url: URL;
  /** Returns the value of a query parameter or null. */
  getQuery: (name: string) => string | null;
  /** Returns all query parameters as an object. */
  getQueries: () => Record<string, string | string[]>;
  /** Returns the parsed JSON body. */
  getJSON: <J = unknown>() => Promise<J>;
  /** Returns the parsed form data. */
  getFormData: () => Promise<FormData>;
  /** Returns a file by name from form data. */
  getFile: (name: string) => Promise<File | null>;
  /** Returns all files from form data. */
  getFiles: () => Promise<File[]>;
  /** Cookie helpers for get/set. */
  cookies: {
    /** Gets a cookie value by name. */
    get: (name: string) => string | undefined;
    /** Sets a cookie value (edge-compatible, sets on response). */
    set: (name: string, value: string, options?: Record<string, unknown>) => void;
  };
  /** Returns the value of a request header or null. */
  getHeader: (name: string) => string | null;
  /** Returns all request headers as a plain object. */
  getHeaders: () => Record<string, string>;
  /** Sets a response header (edge-compatible). */
  setHeader: (name: string, value: string) => void;
  /** Returns the HTTP method. */
  getMethod: () => string;
  /** Returns the request pathname. */
  getPath: () => string;
  /** Returns a redirect response. */
  redirect: (url: string, status?: number) => Response;
  /** Returns a JSON response. */
  json: (data: unknown, status?: number) => Response;
  /** Returns a plain text response. */
  text: (data: string, status?: number) => Response;
  /** Returns a JSON error response (default status 400). */
  sendError: (message: string, status?: number) => Response;
  /** Returns a JSON success response (default status 200). */
  sendSuccess: (data: unknown, status?: number) => Response;
  /** Returns a 401 unauthorized JSON response. */
  sendUnauthorize: (message?: string) => Response;
  /**
   * Validated data from Zod schemas, if validation is enabled.
   */
  validated?: {
    query?: unknown;
    body?: unknown;
    files?: unknown;
  };
  db: typeof useDB;
  dbSchema: typeof DBSchema;  
}

// --- Handler Overloads ---
function defineHandler<Q = unknown, B = unknown, F = unknown>(
  callback: (ctx: HandlerContextTyped<Q, B, F>) => Promise<Response> | Response,
  validation: (z: typeof ZodType) => {
    query?: ZodTypeAny;
    body?: ZodTypeAny;
    files?: Record<string, string | ZodTypeAny>;
  }
): (request: NextRequest, response: NextResponse) => Promise<Response>;
function defineHandler(
  callback: (ctx: HandlerContextUntyped) => Promise<Response> | Response,
  validation?: undefined
): (request: NextRequest, response: NextResponse) => Promise<Response>;
function defineHandler(
  callback: () => Promise<Response> | Response | object,
  validation?: (z: typeof ZodType) => HandlerValidationSchemas
): (request: NextRequest, response: NextResponse) => Promise<Response>;

// --- Implementation ---
function defineHandler<Q = unknown, B = unknown, F = unknown>(
  ...args: [
    ((ctx: HandlerContextTyped<Q, B, F>) => Promise<Response> | Response) | ((ctx: HandlerContextUntyped) => Promise<Response> | Response) | (() => Promise<Response> | Response | object),
    ((z: typeof ZodType) => HandlerValidationSchemas)?
  ]
): (request: NextRequest, response: NextResponse) => Promise<Response> {
  const [callback, validation] = args;
  const cfContext = getCloudflareContext();
  return async (request: NextRequest, response: NextResponse) => {
    const url = new URL(request.url);
    const cookies = {
      get: (name: string): string | undefined => request.cookies.get(name)?.value,
      set: (name: string, value: string, options?: Record<string, unknown>) => {
        response.cookies.set(name, value, options);
      },
    };
    const getHeader = (name: string): string | null => request.headers.get(name);
    const getHeaders = (): Record<string, string> => Object.fromEntries(request.headers.entries());
    const setHeader = (name: string, value: string): void => {
      response.headers.set(name, value);
    };
    const getMethod = (): string => request.method;
    const getPath = (): string => url.pathname;
    const redirect = (to: string, status: number = 302): Response => {
      return NextResponse.redirect(to, status);
    };
    const json = (data: unknown, status: number = 200): Response => {
      return new NextResponse(JSON.stringify(data), {
        status,
        headers: { "content-type": "application/json" },
      });
    };
    const text = (data: string, status: number = 200): Response => {
      return new NextResponse(data, {
        status,
        headers: { "content-type": "text/plain" },
      });
    };
    const sendError = (message: string, status: number = 400): Response => {
      return json({ success: false, error: message }, status);
    };
    const sendSuccess = (data: unknown, status: number = 200): Response => {
      return json({ success: true, data }, status);
    };
    const sendUnauthorize = (message: string = "Unauthorized"): Response => {
      return json({ success: false, error: message }, 401);
    };
    // --- Validation logic ---
    let validated: unknown = undefined;
    let querySchema: ZodTypeAny | undefined;
    let bodySchema: ZodTypeAny | undefined;
    let filesSchema: Record<string, string | ZodTypeAny> | undefined;
    if (validation) {
      const schemas = validation(z);
      querySchema = schemas.query;
      bodySchema = schemas.body;
      filesSchema = schemas.files;
      const v: Record<string, unknown> = {};
      // Query validation
      if (querySchema) {
        const queryObj = Object.fromEntries(url.searchParams.entries());
        const result = querySchema.safeParse(queryObj);
        if (!result.success) {
          return sendError("Invalid query parameters", 400);
        }
        v.query = result.data;
      }
      // Body validation
      if (bodySchema) {
        let bodyObj: unknown = undefined;
        try {
          bodyObj = await request.json();
        } catch {}
        const result = bodySchema.safeParse(bodyObj);
        if (!result.success) {
          return sendError("Invalid body", 400);
        }
        v.body = result.data;
      }
      // Files validation
      if (filesSchema) {
        const form = await request.formData();
        const files: Record<string, File | null> = {};
        for (const key in filesSchema) {
          const file = form.get(key);
          files[key] = file instanceof File ? file : null;
          const rule = filesSchema[key];
          if (typeof rule === 'string') {
            if (file instanceof File) {
              // Parse rule string: ext:jpg,gif|max:10mb
              const parts = rule.split("|");
              let allowedExts: string[] = [];
              let maxSize: number | undefined = undefined;
              for (const part of parts) {
                const trimmed = part.trim();
                if (trimmed.startsWith("ext:")) {
                  allowedExts = trimmed.slice(4).split(",").map(e => e.trim().toLowerCase());
                } else if (trimmed.startsWith("max:")) {
                  const sizeStr = trimmed.slice(4).toLowerCase();
                  const match = sizeStr.match(/^(\d+)(kb|mb|b)?$/);
                  if (match) {
                    let size = parseInt(match[1], 10);
                    const unit = match[2] || "b";
                    if (unit === "kb") size *= 1024;
                    else if (unit === "mb") size *= 1024 * 1024;
                    maxSize = size;
                  }
                }
              }
              // Check extension
              if (allowedExts.length > 0) {
                const fileExt = file.name.split(".").pop()?.toLowerCase() || "";
                if (!allowedExts.includes(fileExt)) {
                  return sendError(`Invalid file extension for '${key}'. Allowed: ${allowedExts.join(", ")}`, 400);
                }
              }
              // Check size
              if (maxSize !== undefined && file.size > maxSize) {
                return sendError(`File '${key}' is too large. Max allowed: ${maxSize} bytes`, 400);
              }
            } else {
              if (rule.includes("required")) {
                return sendError(`File '${key}' is required.`, 400);
              }
            }
          } else if (rule && typeof rule.safeParse === 'function') {
            const result = rule.safeParse(file);
            if (!result.success) {
              return sendError(`File '${key}' failed validation: ${result.error.message}`, 400);
            }
            files[key] = result.data;
          }
        }
        v.files = files;
      }
      validated = v;
    }
    // --- Typed helpers ---
    let ctx: HandlerContextUntyped | HandlerContextTyped<unknown, unknown, unknown>;
    if (validation) {
      ctx = {
        request,
        env: cfContext.env,
        response,
        cf: cfContext.cf,
        url,
        cookies,
        getHeader,
        getHeaders,
        setHeader,
        getMethod,
        getPath,
        redirect,
        json,
        text,
        sendError,
        sendSuccess,
        sendUnauthorize,
        db: useDB,
        dbSchema: DBSchema,
        getQuery: (name: string) => (validated as { query?: Record<string, unknown> })?.query?.[name],
        getQueries: () => (validated as { query?: Record<string, unknown> })?.query,
        getJSON: async () => (validated as { body?: unknown })?.body,
        getFormData: async () => await request.formData(),
        getFile: async (name: string) => (validated as { files?: Record<string, unknown> })?.files?.[name],
        getFiles: async () => (validated as { files?: Record<string, unknown> })?.files,
        validated: validated as { query: unknown; body: unknown; files: unknown },
      } as HandlerContextTyped<unknown, unknown, unknown>;
    } else {
      ctx = {
        request,
        env: cfContext.env,
        response,
        cf: cfContext.cf,
        url,
        cookies,
        getHeader,
        getHeaders,
        setHeader,
        getMethod,
        getPath,
        redirect,
        json,
        text,
        sendError,
        sendSuccess,
        sendUnauthorize,
        db: useDB,
        dbSchema: DBSchema,
        getQuery: (name: string) => url.searchParams.get(name),
        getQueries: () => Object.fromEntries(url.searchParams.entries()),
        getJSON: async () => await request.json(),
        getFormData: async () => await request.formData(),
        getFile: async (name: string) => {
          const form = await request.formData();
          const file = form.get(name);
          return file instanceof File ? file : null;
        },
        getFiles: async () => {
          const form = await request.formData();
          const files: File[] = [];
          for (const value of form.values()) {
            if (value instanceof File) files.push(value);
          }
          return files;
        },
        validated: { query: undefined, body: undefined, files: undefined },
      } as HandlerContextUntyped;
    }
    // --- Call the callback ---
    let callbackResponse;
    if (callback.length > 0) {
      if (validation) {
        callbackResponse = await Promise.resolve((callback as (ctx: HandlerContextTyped<Q, B, F>) => Promise<Response> | Response)(ctx as HandlerContextTyped<Q, B, F>));
      } else {
        callbackResponse = await Promise.resolve((callback as (ctx: HandlerContextUntyped) => Promise<Response> | Response)(ctx as HandlerContextUntyped));
      }
    } else {
      callbackResponse = await Promise.resolve((callback as () => Promise<Response> | Response | object)());
    }
    if (callbackResponse instanceof Response) {
      return callbackResponse;
    }
    return NextResponse.json(callbackResponse);
  };
}

export default defineHandler;