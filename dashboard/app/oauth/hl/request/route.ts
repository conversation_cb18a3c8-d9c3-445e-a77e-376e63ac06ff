import { NextRequest } from "next/server";

export const GET = async (request: NextRequest) => {
    const url = new URL(request.url);
    let redirectURL = ''
    if (url.hostname === 'localhost') {
        redirectURL = 'http://localhost:4095/oauth/hl/callback'
    } else {
        redirectURL = 'https://godashify.com/oauth/hl/callback'
    }

    return Response.redirect(`https://marketplace.leadconnectorhq.com/oauth/chooselocation?response_type=code&redirect_uri=${encodeURIComponent(redirectURL)}&client_id=672212350aa8cb4677f2f575-m2vsb7x6&scope=locations.write+locations.readonly+oauth.write+oauth.readonly+saas%2Flocation.read+saas%2Flocation.write+saas%2Fcompany.read+saas%2Fcompany.write+snapshots.readonly+snapshots.write+users.readonly+users.write+businesses.readonly+businesses.write+companies.readonly+calendars.readonly+calendars.write+calendars%2Fevents.readonly+calendars%2Fevents.write+calendars%2Fgroups.readonly+calendars%2Fgroups.write+calendars%2Fresources.readonly+campaigns.readonly+calendars%2Fresources.write+conversations.readonly+conversations.write+conversations%2Fmessage.readonly+conversations%2Fmessage.write+contacts.readonly+conversations%2Freports.readonly+contacts.write+objects%2Fschema.readonly+objects%2Fschema.write+objects%2Frecord.readonly+objects%2Frecord.write+courses.write+courses.readonly+forms.readonly+forms.write+invoices.readonly+invoices.write+invoices%2Fschedule.readonly+invoices%2Fschedule.write+invoices%2Ftemplate.readonly+invoices%2Ftemplate.write+links.readonly+lc-email.readonly+links.write+locations%2FcustomValues.readonly+locations%2FcustomValues.write+locations%2FcustomFields.readonly+locations%2FcustomFields.write+locations%2Ftasks.readonly+locations%2Ftasks.write+locations%2Ftags.readonly+locations%2Ftags.write+locations%2Ftemplates.readonly+medias.readonly+medias.write+funnels%2Fredirect.readonly+funnels%2Fpage.readonly+funnels%2Ffunnel.readonly+funnels%2Fpagecount.readonly+funnels%2Fredirect.write+opportunities.readonly+opportunities.write+payments%2Forders.readonly+payments%2Forders.write+payments%2Fintegration.readonly+payments%2Fintegration.write+payments%2Ftransactions.readonly+payments%2Fsubscriptions.readonly+payments%2Fcustom-provider.readonly+payments%2Fcustom-provider.write+products.readonly+products.write+products%2Fprices.readonly+products%2Fprices.write+products%2Fcollection.readonly+products%2Fcollection.write+socialplanner%2Foauth.readonly+socialplanner%2Foauth.write+socialplanner%2Fpost.readonly+socialplanner%2Fpost.write+socialplanner%2Faccount.readonly+socialplanner%2Faccount.write+socialplanner%2Fcsv.readonly+socialplanner%2Fcsv.write+socialplanner%2Fcategory.readonly+socialplanner%2Ftag.readonly+store%2Fshipping.readonly+store%2Fshipping.write+store%2Fsetting.readonly+store%2Fsetting.write+surveys.readonly+workflows.readonly+wordpress.site.readonly+blogs%2Fpost.write+blogs%2Fpost-update.write+blogs%2Fcheck-slug.readonly+blogs%2Fcategory.readonly+blogs%2Fauthor.readonly+socialplanner%2Fcategory.write+socialplanner%2Ftag.write+custom-menu-link.readonly+custom-menu-link.write+blogs%2Fposts.readonly+blogs%2Flist.readonly+emails%2Fbuilder.write+emails%2Fbuilder.readonly+emails%2Fschedule.readonly+payments%2Fcoupons.write+payments%2Fcoupons.readonly+invoices%2Festimate.write+invoices%2Festimate.readonly+associations%2Frelation.write+associations%2Frelation.readonly+associations.readonly+associations.write+conversations%2Flivechat.write`)
}