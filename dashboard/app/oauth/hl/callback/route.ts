// import { NextRequest } from "next/server";
// import { getCloudflareContext } from "@opennextjs/cloudflare";

import defineHandler from "@/lib/requestHandler"

// export const GET = async (request: NextRequest) => {
//     const cfContext = getCloudflareContext()
//     const url = new URL(request.url);
//     const code = url.searchParams.get("code");
//     const clientId = cfContext.env.HL_CLIENT_ID;
//     const clientSecret = cfContext.env.HL_CLIENT_SECRET;

//     const response = await fetch(`https://api.leadconnectorhq.com/oauth/token`, {
//         method: 'POST',
//         headers: {
//             'Content-Type': 'application/x-www-form-urlencoded'
//         },
//         body: new URLSearchParams({
//             code: code || '',
//             client_id: clientId,
//             client_secret: clientSecret,
//             grant_type: 'authorization_code',
//             user_type: "Company"
//         })
//     })

//     const data: HLCredentialsResponse = await response.json()

//     console.log(data)

//     return Response.json(data);
// }
export const GET = defineHandler(async (ctx) => {
    const code = ctx.getQuery("code") as string;
    
    return ctx.json({
        message: "Hello, world!"
    })
}, (z) => ({
    query: z.object({
        code: z.string(),
    })
}))