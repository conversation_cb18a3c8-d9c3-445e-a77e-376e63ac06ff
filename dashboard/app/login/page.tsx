"use client";
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth, useAppDispatch, useAppSelector, submitLoginForm, submitOtp, resendOtp } from '@gd/core';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [otp, setOtp] = useState('');

  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const dispatch = useAppDispatch();

  // Get auth state from Redux
  const {
    currentPage,
    isLoading,
    loginError,
    availableOtpChannels,
    selectedOtpChannel,
    isResendingOtp
  } = useAppSelector((state) => state.auth);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/');
    }
  }, [isAuthenticated, router]);

  const handleLoginSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      return;
    }

    try {
      await dispatch(submitLoginForm({
        email,
        password,
        otpChannel: 'email'
      })).unwrap();
      // If successful, Redux will update currentPage to 'otp'
    } catch (error) {
      console.error('Login error:', error);
      // Error is handled by Redux and stored in loginError
    }
  };

  const handleOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!otp || otp.length !== 6) {
      return;
    }

    try {
      await dispatch(submitOtp(otp)).unwrap();
      // If successful, user will be authenticated and redirected by AuthGuard
    } catch (error) {
      console.error('OTP error:', error);
      // Error is handled by Redux and stored in loginError
    }
  };

  const handleChannelChange = async (channel: "email" | "phone") => {
    try {
      await dispatch(resendOtp(channel)).unwrap();
      // Clear current OTP input when switching channels
      setOtp('');
    } catch (error) {
      console.error('Channel switch error:', error);
      // Error is handled by Redux and stored in loginError
    }
  };

  const handleResendOtp = async () => {
    try {
      await dispatch(resendOtp(selectedOtpChannel)).unwrap();
      // Clear current OTP input when resending
      setOtp('');
    } catch (error) {
      console.error('Resend OTP error:', error);
      // Error is handled by Redux and stored in loginError
    }
  };

  if (currentPage === "otp") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Enter Verification Code
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              We&apos;ve sent a verification code to your {selectedOtpChannel === 'email' ? 'email address' : 'phone number'}.
            </p>
          </div>

          {loginError && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-800">{loginError}</p>
            </div>
          )}

          <form className="mt-8 space-y-6" onSubmit={handleOtpSubmit}>
            <div>
              <label htmlFor="otp" className="sr-only">
                Verification Code
              </label>
              <input
                id="otp"
                name="otp"
                type="text"
                maxLength={6}
                required
                className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm text-center text-lg tracking-widest"
                placeholder="000000"
                value={otp}
                onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
                disabled={isLoading || isResendingOtp}
              />
            </div>

            {/* OTP Channel Selection */}
            {availableOtpChannels.length > 1 && (
              <div className="space-y-3">
                <p className="text-sm text-gray-700 text-center">Choose delivery method:</p>
                <div className="flex space-x-4 justify-center">
                  {availableOtpChannels.map((channel) => (
                    <button
                      key={channel}
                      type="button"
                      onClick={() => handleChannelChange(channel)}
                      disabled={isLoading || isResendingOtp || selectedOtpChannel === channel}
                      className={`px-4 py-2 text-sm font-medium rounded-md border ${
                        selectedOtpChannel === channel
                          ? 'bg-indigo-600 text-white border-indigo-600'
                          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                      } disabled:opacity-50 disabled:cursor-not-allowed`}
                    >
                      {isResendingOtp && selectedOtpChannel !== channel ? 'Switching...' :
                       channel === 'email' ? '📧 Email' : '📱 Phone'}
                    </button>
                  ))}
                </div>
              </div>
            )}

            <div>
              <button
                type="submit"
                disabled={isLoading || isResendingOtp || otp.length !== 6}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Verifying...' : 'Verify Code'}
              </button>
            </div>

            {/* Resend OTP Button */}
            <div className="text-center">
              <button
                type="button"
                onClick={handleResendOtp}
                disabled={isLoading || isResendingOtp}
                className="text-sm text-indigo-600 hover:text-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isResendingOtp ? 'Resending...' : `Resend code to ${selectedOtpChannel === 'email' ? 'email' : 'phone'}`}
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
        </div>

        {loginError && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-800">{loginError}</p>
          </div>
        )}

        <form className="mt-8 space-y-6" onSubmit={handleLoginSubmit}>
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="email" className="sr-only">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                placeholder="Email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading}
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={isLoading}
              />
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading || !email || !password}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Signing in...' : 'Sign in'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
