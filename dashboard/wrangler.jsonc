/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "dashboard",
	"main": ".open-next/worker.js",
	"compatibility_date": "2025-03-01",
	"compatibility_flags": [
		"nodejs_compat",
		"global_fetch_strictly_public"
	],
	"assets": {
		"binding": "ASSETS",
		"directory": ".open-next/assets"
	},
	"observability": {
		"enabled": true
	},
	"services": [
		{
			"binding": "WORKER_SELF_REFERENCE",
			"service": "dashboard"
		}
	],
	/**
	 * Smart Placement
	 * Docs: https://developers.cloudflare.com/workers/configuration/smart-placement/#smart-placement
	 */
	// "placement": { "mode": "smart" },
	// R2 incremental cache
	"r2_buckets": [
		{
			"binding": "NEXT_INC_CACHE_R2_BUCKET",
			"bucket_name": "dashboard",
		},
	],
	/**
	* DO Queue
	* Uncomment this only in production
	*/
	//   "durable_objects": {
	// 	"bindings": [
	// 	  {
	// 		"name": "NEXT_CACHE_DO_QUEUE",
	// 		"class_name": "DOQueueHandler",
	// 	  },
	// 	  // This is only required if you use On-demand revalidation
	// 	  {
	// 		"name": "NEXT_TAG_CACHE_DO_SHARDED",
	// 		"class_name": "DOShardedTagCache",
	// 	  },
	// 	  {
	// 		"name": "NEXT_CACHE_DO_PURGE",
	// 		"class_name": "BucketCachePurge",
	// 	  },
	// 	],
	//   },
	"migrations": [
		{
			"tag": "v1",
			"new_sqlite_classes": [
				"DOQueueHandler",
				// This is only required if you use On-demand revalidation
				"DOShardedTagCache",
				"BucketCachePurge",
			],
		},
	],
	// D1 Tag Cache (Next mode)
	// This is only required if you use On-demand revalidation
	"d1_databases": [
		{
			"binding": "NEXT_TAG_CACHE_D1",
			"database_id": "6d79595d-31c8-4af4-9f10-5e83937729d1",
			"database_name": "dashboard",
		},
	],
	/**
	 * Environment Variables
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables
	 */
	// "vars": { "MY_VARIABLE": "production_value" },
	/**
	 * Note: Use secrets to store sensitive data.
	 * https://developers.cloudflare.com/workers/configuration/secrets/
	 */
	"vars": {
		"NEON_DB_URL": "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require",
		"HL_CLIENT_ID": "672212350aa8cb4677f2f575-mbwkznue",
		"HL_CLIENT_SECRET": "ec9fa94b-562a-487e-bf31-357e40a0fecf"
	}
}